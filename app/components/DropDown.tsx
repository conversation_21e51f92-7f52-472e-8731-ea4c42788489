import {
  Di<PERSON>sions,
  Modal,
  ScrollView,
  StyleProp,
  StyleSheet,
  View,
  ViewStyle,
} from "react-native";
import P from "./P";
import { TouchableOpacity } from "react-native";
import { colors } from "../config/colors";
import { useState } from "react";
import { fonts } from "../config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import { TouchableWithoutFeedback } from "react-native-gesture-handler";
import { Text } from "react-native";

interface DropDownItem {
  id: number;
  lable: string;
  value: string;
}

interface PProps {
  label?: string;
  defaultValue?: string;
  showBackArrow?: boolean;
  headerText?: string;
  headerStyle?: StyleProp<ViewStyle>;
  data?: DropDownItem[];
  onItemSelect?: (item: DropDownItem) => void;
}

const { width, height } = Dimensions.get("window");
export default function DropDown({
  label,
  defaultValue,
  showBackArrow,
  headerText,
  headerStyle,
  data,
  onItemSelect,
}: //   data
PProps) {
  const [selectedValue, setSelectedValue] = useState("");
  const [selectedItem, setSelectedItem] = useState<DropDownItem | null>(null);
  const [showModal, setShowModal] = useState(false);

  return (
    <>
      <View style={[{ width: "100%" }]}>
        <P>{label}</P>
        <TouchableOpacity
          style={[styles.dropClick]}
          onPress={() => {
            setShowModal(true);
          }}
        >
          <P style={{ fontFamily: fonts.poppinsRegular }}>
            {selectedValue === "" ? defaultValue : selectedValue}
          </P>
          <SvgXml xml={svg.dropIcon} />
        </TouchableOpacity>
      </View>
      {showModal && (
        <Modal
          visible={showModal}
          animationType="slide"
          statusBarTranslucent={true}
          transparent
          style={{ flex: 1 }}
          onRequestClose={() => {
            setShowModal(false);
          }}
        >
          <TouchableWithoutFeedback
            onPress={() => {
              setShowModal(false);
            }}
            style={styles.overLay}
          ></TouchableWithoutFeedback>
          <View style={styles.modalCard}>
            <View style={[styles.section1, headerStyle]}>
              {showBackArrow && (
                <TouchableOpacity
                  onPress={() => {
                    setShowModal(false);
                  }}
                >
                  <SvgXml xml={svg.goBackIcon} style={{ marginRight: 12 }} />
                </TouchableOpacity>
              )}

              <Text
                style={{
                  fontSize: 14,
                  fontFamily: fonts.poppinsMedium,
                  lineHeight: 21,
                }}
              >
                {headerText}
              </Text>
              <TouchableOpacity
                style={{ position: "absolute", right: 24 }}
                onPress={() => {
                  setShowModal(false);
                }}
              >
                <SvgXml xml={svg.xClose} />
              </TouchableOpacity>
            </View>
            <ScrollView>
              <View style={styles.MainContent}>
                {data.map((item, index) => (
                  <TouchableOpacity
                    key={index}
                    onPress={() => {
                      setSelectedValue(item?.lable);
                      setSelectedItem(item);
                      setShowModal(false);
                      if (onItemSelect) {
                        onItemSelect(item);
                      }
                    }}
                    style={{ paddingVertical: 10, paddingHorizontal: 16 }}
                  >
                    <P style={{ fontFamily: fonts.poppinsRegular }}>
                      {item?.lable}
                    </P>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
          </View>
        </Modal>
      )}
    </>
  );
}

const styles = StyleSheet.create({
  dropClick: {
    width: "100%",
    borderWidth: 1,
    borderColor: colors.stroke,
    marginTop: 6,
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 14,
    paddingVertical: 11,
    minHeight: 44,
    borderRadius: 8,
    flexDirection: "row",
  },
  overLay: {
    width: width,
    height: height,
    backgroundColor: colors.overlay,
    justifyContent: "flex-end",
  },
  modalCard: {
    width: "100%",
    minHeight: "30%",
    backgroundColor: colors.white,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    position: "absolute",
    bottom: 0,
    maxHeight: "90%",
  },
  section1: {
    height: 61,
    // backgroundColor: 'red',
    width: "100%",
    borderBottomColor: "rgba(139, 144, 154, 0.25)",
    borderBottomWidth: 1,
    flexDirection: "row",
    alignItems: "center",
    marginTop: 11,
    paddingLeft: 24,
    paddingRight: 24,
  },
  MainContent: {
    width: "100%",
    // backgroundColor: "red",
    marginTop: 8,
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
});
