import { ScrollView, StyleSheet, View } from "react-native";
import Div from "../../components/Div";
import { colors } from "../../config/colors";
import AuthenticationHeader from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import H4 from "../../components/H4";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import DropDown from "../../components/DropDown";
import Button from "../../components/Button";

export default function CardPurchaseInfoScreen({ navigation }) {
  const occupations = [
    { id: 1, lable: "Student", value: "Student" },
    { id: 2, lable: "Business", value: "Business" },
  ];
  const reasons = [
    { id: 1, lable: "Student", value: "Student" },
    { id: 2, lable: "Business", value: "Business" },
  ];
  const monthlySpendings = [
    { id: 1, lable: "$10 - $50", value: "$10 - $50" },
    { id: 2, lable: "$50 - $100", value: "$50 - $100" },
    { id: 3, lable: "$10 - $50", value: "$10 - $50" },
    { id: 4, lable: "$50 - $100", value: "$50 - $100" },
    { id: 5, lable: "$10 - $50", value: "$10 - $50" },
    { id: 6, lable: "$50 - $100", value: "$50 - $100" },
    { id: 7, lable: "$50 - $100", value: "$50 - $100" },
  ];
  const annualIncome = [
    { id: 1, lable: "$10 - $50", value: "$10 - $50" },
    { id: 2, lable: "$50 - $100", value: "$50 - $100" },
    { id: 3, lable: "$10 - $50", value: "$10 - $50" },
    { id: 4, lable: "$50 - $100", value: "$50 - $100" },
    { id: 5, lable: "$10 - $50", value: "$10 - $50" },
    { id: 6, lable: "$50 - $100", value: "$50 - $100" },
    { id: 7, lable: "$50 - $100", value: "$50 - $100" },
  ];
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHeader
          text="Card registration"
          navigation={navigation}
        />
        <ScrollView
          style={{ width: "100%" }}
          contentContainerStyle={{ paddingBottom: 120 }}
          showsVerticalScrollIndicator={false}
        >
          <View
            style={{
              width: "80%",
              alignSelf: "center",
              alignItems: "center",
              marginTop: 20,
            }}
          >
            <SvgXml xml={svg.userIllustrate} />
            <H4
              style={{ fontFamily: fonts.poppinsSemibold, textAlign: "center" }}
            >
              Tell us a bit about yourself
            </H4>
            <P
              style={{ fontFamily: fonts.poppinsRegular, textAlign: "center" }}
            >
              Get the SFx Card That Matches Your Lifestyle
            </P>
          </View>
          <View
            style={{
              width: "90%",
              alignSelf: "center",
              backgroundColor: colors.white,
              padding: 16,
              marginTop: 32,
              borderRadius: 12,
            }}
          >
            <View>
              <DropDown
                label="Occupation"
                defaultValue="Select Occupation"
                headerText="Occupation"
                data={occupations}
              />
            </View>
            <View style={styles.mg16}>
              <DropDown
                label="What are you using card for?"
                defaultValue="Select reason"
                headerText="Spending"
                data={reasons}
              />
            </View>
            <View style={styles.mg16}>
              <DropDown
                label="Monthly spending"
                defaultValue=""
                headerText="Monthly spending"
                data={monthlySpendings}
              />
            </View>
            <View style={styles.mg16}>
              <DropDown label="Annual income" headerText="Annual income" />
            </View>
          </View>
          <Button
            style={{ marginTop: 32, width: "80%", alignSelf: "center" }}
            btnText="Continue"
          />
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  mg16: {
    marginTop: 16,
  },
});
