import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Div from "../../components/Div";
import { colors } from "../../config/colors";
import AuthenticationHeader from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import H4 from "../../components/H4";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import DropDown from "../../components/DropDown";
import Button from "../../components/Button";
import { useState } from "react";
import { Linking } from "react-native";

export default function CardPurchaseInfoScreen({ navigation }) {
  const [selectedOccupation, setSelectedOccupation] = useState(null);
  const [selectedReason, setSelectedReason] = useState(null);
  const [selectedMonthlySpending, setSelectedMonthlySpending] = useState(null);
  const [selectedAnnualIncome, setSelectedAnnualIncome] = useState(null);
  const [isChecked, setIsChecked] = useState(false);

  const handleContinue = () => {
    navigation.navigate("ConfirmCardPurchaseDetails")
    console.log("All selected items:", {
      occupation: selectedOccupation,
      reason: selectedReason,
      monthlySpending: selectedMonthlySpending,
      annualIncome: selectedAnnualIncome,
    });
  };
  const occupations = [
    { id: 1, lable: "Student", value: "Student" },
    { id: 2, lable: "Business", value: "Business" },
  ];
  const reasons = [
    { id: 1, lable: "Student", value: "Student" },
    { id: 2, lable: "Business", value: "Business" },
  ];
  const monthlySpendings = [
    { id: 1, lable: "$10 - $50", value: "$10 - $50" },
    { id: 2, lable: "$50 - $100", value: "$50 - $100" },
    { id: 3, lable: "$10 - $50", value: "$10 - $50" },
    { id: 4, lable: "$50 - $100", value: "$50 - $100" },
    { id: 5, lable: "$10 - $50", value: "$10 - $50" },
    { id: 6, lable: "$50 - $100", value: "$50 - $100" },
    { id: 7, lable: "$50 - $100", value: "$50 - $100" },
  ];
  const annualIncome = [
    { id: 1, lable: "$10 - $50", value: "$10 - $50" },
    { id: 2, lable: "$50 - $100", value: "$50 - $100" },
    { id: 3, lable: "$10 - $50", value: "$10 - $50" },
    { id: 4, lable: "$50 - $100", value: "$50 - $100" },
    { id: 5, lable: "$10 - $50", value: "$10 - $50" },
    { id: 6, lable: "$50 - $100", value: "$50 - $100" },
    { id: 7, lable: "$50 - $100", value: "$50 - $100" },
  ];
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHeader
          text="Card registration"
          navigation={navigation}
        />
        <ScrollView
          style={{ width: "100%" }}
          contentContainerStyle={{ paddingBottom: 120 }}
          showsVerticalScrollIndicator={false}
        >
          <View
            style={{
              width: "80%",
              alignSelf: "center",
              alignItems: "center",
              marginTop: 20,
            }}
          >
            <SvgXml xml={svg.userIllustrate} />
            <H4
              style={{ fontFamily: fonts.poppinsSemibold, textAlign: "center" }}
            >
              Tell us a bit about yourself
            </H4>
            <P
              style={{ fontFamily: fonts.poppinsRegular, textAlign: "center" }}
            >
              Get the SFx Card That Matches Your Lifestyle
            </P>
          </View>
          <View
            style={{
              width: "90%",
              alignSelf: "center",
              backgroundColor: colors.white,
              padding: 16,
              marginTop: 32,
              borderRadius: 12,
            }}
          >
            <View>
              <DropDown
                label="Occupation"
                defaultValue="Select Occupation"
                headerText="Occupation"
                data={occupations}
                onItemSelect={(item) => {
                  setSelectedOccupation(item);
                  console.log("Selected occupation:", item);
                }}
              />
            </View>
            <View style={styles.mg16}>
              <DropDown
                label="What are you using card for?"
                defaultValue="Select reason"
                headerText="Spending"
                data={reasons}
                onItemSelect={(item) => {
                  setSelectedReason(item);
                  console.log("Selected reason:", item);
                }}
              />
            </View>
            <View style={styles.mg16}>
              <DropDown
                label="Monthly spending"
                defaultValue=""
                headerText="Monthly spending"
                data={monthlySpendings}
                onItemSelect={(item) => {
                  setSelectedMonthlySpending(item);
                  console.log("Selected monthly spending:", item);
                }}
              />
            </View>
            <View style={styles.mg16}>
              <DropDown
                label="Annual income"
                headerText="Annual income"
                data={annualIncome}
                onItemSelect={(item) => {
                  setSelectedAnnualIncome(item);
                  console.log("Selected annual income:", item);
                }}
              />
            </View>
            <View
              style={{
                marginTop: 16,
                flexDirection: "row",
                gap: 8,
              }}
            >
              <TouchableOpacity onPress={()=>{setIsChecked(!isChecked)}}>
                <SvgXml xml={isChecked ? svg.checkedBox : svg.checkBox} />
              </TouchableOpacity>
              <Text
                style={{
                  fontSize: 14,
                  fontFamily: fonts.poppinsRegular,
                  flex: 1,
                }}
              >
                Yes, I agree to SFx’s card{" "}
                <Text
                  onPress={() => {
                    Linking.openURL("https://www.sfxchange.co/en/privacy");
                  }}
                  style={{ color: colors.primary }}
                >
                  Privacy Policy
                </Text>{" "}
                &{" "}
                <Text
                  onPress={() => {
                    Linking.openURL("https://www.sfxchange.co/en/privacy");
                  }}
                  style={{ color: colors.primary }}
                >
                  Terms of Service
                </Text>
              </Text>
            </View>
          </View>
          <Button
            style={{ marginTop: 32, width: "80%", alignSelf: "center" }}
            btnText="Continue"
            onPress={handleContinue}
          />
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  mg16: {
    marginTop: 16,
  },
});
