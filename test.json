{"account": "valstark", "country": "NG", "createdAt": "2024-11-30T05:31:32.405Z", "id": "674aa334030845f62249659d", "isManualNetworkInput": false, "name": "<PERSON>", "providerName": "SFx money app", "sfxBeneficiaryUser": {"_2faEnabled": false, "adminAcctSetup": false, "beneficiaries": [], "bitpowerSubAccountId": "709aa0c0-4bbf-4e3d-bd52-a4f77772d61a", "bridgeCardflagForManualReview": false, "cardHolderVerified": false, "cards": [], "createdAt": "2024-11-04T12:14:36.135Z", "dob": "2003-04-04T00:00:00.000Z", "email": "<EMAIL>", "emailnotification": false, "firstName": "<PERSON>", "googleId": "111823433094473077585", "hasPin": true, "homeCountry": "Nigeria", "id": "6728baac384d607d28671087", "isAppleUser": false, "isFaceIdActive": false, "isGoogleUser": true, "kyc": ["674a9fe2030845f622495f38"], "lastName": "Val", "phoneNumber": "+*************", "picture": "http://res.cloudinary.com/ddrsfwzlk/image/upload/v1744123984/Users/<USER>", "pushnotification": true, "referralCode": "7EAF8117", "residentAddress": "<PERSON><PERSON>", "role": "user", "tier": {"code": "1012", "level": 1, "message": "ID Number Validated"}, "updatedAt": "2025-06-03T18:28:12.448Z", "userStatus": "active", "username": "valstark", "verified": "true"}, "status": "active", "type": "sfx-money-app", "updatedAt": "2024-11-30T05:31:32.405Z", "user": "66efbd7147f7fcb48cfda6a7"}